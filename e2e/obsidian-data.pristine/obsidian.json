{"vaults": {"test-vault": {"path": "VAULT_PATH_PLACEHOLDER", "ts": 1693574400000, "open": true}}, "frame": {"x": 0, "y": 0, "width": 1280, "height": 1024}, "theme": "obsidian", "enabledPlugins": ["obsidian-task-sync"], "hotkeys": {}, "workspaceLayout": {"type": "split", "direction": "vertical", "children": [{"type": "leaf", "state": {"type": "empty", "state": {}}}]}, "leftRibbon": {"hiddenItems": {}}, "rightRibbon": {"hiddenItems": {}}, "leftSidedock": {"children": [], "collapsed": true}, "rightSidedock": {"children": [], "collapsed": true}, "active": "test-vault", "lastOpenFiles": []}