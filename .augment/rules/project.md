---
type: "always_apply"
---

## Current Project Context

Project: Task Sync
Testing Framework: vites<PERSON> and Playwright

## Testing

- `npm test` to run unit and integration tests
- `npm run test:e2e:headless` to run end-to-end tests
- Leverage extra logging or screenshots when debugging end-to-end tests

## Testing rules

- In e2e tests you must not use timeouts longer than 5 seconds

## References

- See tmp/obsidian-ghost-sync plugin for reference on testing setup, helpers and test running tasks
